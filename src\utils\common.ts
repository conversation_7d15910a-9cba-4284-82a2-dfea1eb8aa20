/**
 * 通用工具函数
 */

/**
 * 格式化文件大小
 */
export const formatFileSize = (size: number): string => {
  if (!size || size === 0) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

/**
 * 格式化视频时长
 */
export const formatDuration = (duration: number): string => {
  if (!duration || duration === 0) return '00:00'
  
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = Math.floor(duration % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * 格式化时间戳
 */
export const formatTime = (timestamp: string | Date, format = 'relative'): string => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (format === 'relative') {
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    if (diff < 86400000 * 7) return `${Math.floor(diff / 86400000)}天前`
    
    // 超过一周显示具体日期
    return formatTime(timestamp, 'date')
  }
  
  if (format === 'date') {
    return `${date.getMonth() + 1}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }
  
  if (format === 'full') {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
  }
  
  return date.toString()
}

/**
 * 获取状态文本
 */
export const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'pending': '等待中',
    'uploading': '上传中'
  }
  
  return statusMap[status] || '未知状态'
}

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'processing': '#1890ff',
    'completed': '#52c41a',
    'failed': '#ff4d4f',
    'pending': '#faad14',
    'uploading': '#722ed1'
  }
  
  return colorMap[status] || '#d9d9d9'
}

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

/**
 * 深拷贝
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

/**
 * 生成唯一ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

/**
 * 获取设备ID
 */
export const getDeviceId = (): string => {
  // 尝试从本地存储获取设备ID
  let deviceId = uni.getStorageSync('deviceId')

  if (!deviceId) {
    // 如果没有设备ID，则生成一个新的
    try {
      const systemInfo = uni.getSystemInfoSync()
      const deviceInfo = {
        platform: systemInfo.platform,
        model: systemInfo.model,
        system: systemInfo.system,
        version: systemInfo.version,
        timestamp: Date.now()
      }

      // 生成基于设备信息的唯一标识
      deviceId = btoa(JSON.stringify(deviceInfo)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)

      // 保存到本地存储
      uni.setStorageSync('deviceId', deviceId)
    } catch (error) {
      // 如果获取系统信息失败，使用随机ID
      deviceId = generateId()
      uni.setStorageSync('deviceId', deviceId)
    }
  }

  return deviceId
}

/**
 * 验证视频文件
 */
export const validateVideoFile = (file: any): { valid: boolean; message?: string } => {
  if (!file) {
    return { valid: false, message: '请选择视频文件' }
  }
  
  // 检查文件大小 (100MB)
  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    return { valid: false, message: '视频文件不能超过100MB' }
  }
  
  // 检查时长 (5分钟)
  const maxDuration = 300
  if (file.duration > maxDuration) {
    return { valid: false, message: '视频时长不能超过5分钟' }
  }
  
  return { valid: true }
}

/**
 * 错误处理
 */
export const handleError = (error: any, defaultMessage = '操作失败'): void => {
  console.error('Error:', error)
  
  let message = defaultMessage
  
  if (typeof error === 'string') {
    message = error
  } else if (error?.errMsg) {
    message = error.errMsg
  } else if (error?.message) {
    message = error.message
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示加载提示
 */
export const showLoading = (title = '加载中...'): void => {
  uni.showLoading({ title })
}

/**
 * 隐藏加载提示
 */
export const hideLoading = (): void => {
  uni.hideLoading()
}

/**
 * 显示成功提示
 */
export const showSuccess = (title: string): void => {
  uni.showToast({
    title,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示确认对话框
 */
export const showConfirm = (
  content: string,
  title = '确认'
): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}
