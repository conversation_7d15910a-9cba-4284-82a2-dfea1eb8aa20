<template>
  <view class="login-container">
    <!-- 头部区域 -->
    <view class="hero-section">
      <view class="hero-content">
        <view class="hero-text">
          <text class="hero-title">用户登录系统</text>
          <text class="hero-subtitle">微信一键登录，安全便捷</text>
          <text class="hero-description">体验完整的用户身份验证功能</text>
        </view>
        <view class="hero-image">
          <view class="logo-placeholder">🔐</view>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="hero-action">
        <button v-if="!userInfo.isLogin" @click="handleWechatLogin" class="btn btn-primary btn-lg login-btn">
          <text class="btn-icon">👤</text>
          <text>微信登录</text>
        </button>
        <view v-else class="user-info-card">
          <image class="user-avatar" :src="userInfo.avatar || '/static/default-avatar.svg'" mode="aspectFill" />
          <view class="user-details">
            <text class="user-name">{{ userInfo.nickname }}</text>
            <text class="user-status">已登录</text>
          </view>
        </view>
        <text class="hero-tip">{{ userInfo.isLogin ? '登录成功，可以使用完整功能' : '点击按钮进行微信授权登录' }}</text>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="features-section">
      <view class="section-header">
        <text class="section-title">登录功能</text>
        <text class="section-subtitle">完整的用户身份验证体系</text>
      </view>

      <view class="features-grid">
        <view class="feature-card" v-for="(feature, index) in features" :key="index">
          <view class="feature-icon-wrapper">
            <text class="feature-icon">{{ feature.icon }}</text>
          </view>
          <view class="feature-content">
            <text class="feature-title">{{ feature.title }}</text>
            <text class="feature-desc">{{ feature.desc }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户状态 -->
    <view v-if="userInfo.isLogin" class="status-section">
      <view class="section-header">
        <text class="section-title">登录状态</text>
      </view>

      <view class="status-card card">
        <view class="status-content">
          <view class="status-item">
            <text class="status-label">设备ID:</text>
            <text class="status-value">{{ userInfo.deviceId.slice(-8) }}</text>
          </view>
          <view class="status-item">
            <text class="status-label">OpenID:</text>
            <text class="status-value">{{ userInfo.openid.slice(-8) }}</text>
          </view>
          <view class="status-item">
            <text class="status-label">登录状态:</text>
            <text class="status-value success">已登录</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">使用说明</text>
        <text class="section-subtitle">完整的登录功能演示</text>
      </view>

      <view class="guide-content">
        <view class="guide-item">
          <view class="guide-number">1</view>
          <view class="guide-text">
            <text class="guide-title">微信授权登录</text>
            <text class="guide-desc">点击登录按钮进行微信授权</text>
          </view>
        </view>
        <view class="guide-item">
          <view class="guide-number">2</view>
          <view class="guide-text">
            <text class="guide-title">获取用户信息</text>
            <text class="guide-desc">自动获取并保存用户资料</text>
          </view>
        </view>
        <view class="guide-item">
          <view class="guide-number">3</view>
          <view class="guide-text">
            <text class="guide-title">查看个人中心</text>
            <text class="guide-desc">在"我的"页面查看详细信息</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getWechatOpenid, updateUserInfo } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 功能特性数据
const features = ref([
  {
    icon: '🔐',
    title: '微信登录',
    desc: '安全便捷的微信授权登录'
  },
  {
    icon: '👤',
    title: '用户管理',
    desc: '完整的用户信息管理功能'
  },
  {
    icon: '🔒',
    title: '身份验证',
    desc: '可靠的用户身份验证体系'
  },
  {
    icon: '📱',
    title: '设备绑定',
    desc: '设备唯一标识与用户绑定'
  }
])

// 页面加载时获取用户信息
onMounted(() => {
  loadUserInfo()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 微信登录
const handleWechatLogin = async () => {
  try {
    uni.showLoading({ title: '登录中...' })

    // 第一步：调用 uni.login 获取 code
    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    console.log('获取到微信登录code:', loginRes.code)

    // 第二步：调用服务端接口获取 openid
    uni.showLoading({ title: '获取用户信息中...' })

    const openidResult = await getWechatOpenid(loginRes.code)
    console.log('获取openid结果:', openidResult)

    // 验证返回结果
    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    if (!openidResult.data || !openidResult.data.openid) {
      throw new Error('获取用户openid失败')
    }

    const { openid, userId, isNewUser } = openidResult.data

    // 第三步：获取用户信息授权
    uni.showLoading({ title: '获取用户资料中...' })

    const userProfile = await new Promise<any>((resolve, reject) => {
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })

    // 第四步：更新服务端用户信息
    if (userProfile.userInfo.nickName || userProfile.userInfo.avatarUrl) {
      try {
        await updateUserInfo(
          openid,
          userProfile.userInfo.nickName,
          userProfile.userInfo.avatarUrl
        )
        console.log('用户信息更新成功')
      } catch (updateError) {
        console.warn('更新用户信息失败，但不影响登录:', updateError)
      }
    }

    // 第五步：更新本地用户信息
    userInfo.value.nickname = userProfile.userInfo.nickName
    userInfo.value.avatar = userProfile.userInfo.avatarUrl
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)

    uni.hideLoading()

    // 显示登录成功提示
    const successMessage = isNewUser ? '注册成功' : '登录成功'
    uni.showToast({
      title: successMessage,
      icon: 'success'
    })

    console.log('微信登录完成:', {
      openid: openid,
      nickname: userInfo.value.nickname,
      isNewUser: isNewUser
    })

  } catch (error: any) {
    uni.hideLoading()
    console.error('微信登录失败:', error)

    let errorMessage = '登录失败'

    // 处理不同类型的错误
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMessage = '用户拒绝授权'
      } else if (error.errMsg.includes('auth cancel')) {
        errorMessage = '用户取消登录'
      }
    } else if (error.message) {
      if (error.message.includes('获取登录凭证失败')) {
        errorMessage = '获取登录凭证失败，请重试'
      } else if (error.message.includes('获取用户openid失败')) {
        errorMessage = '获取用户信息失败，请重试'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding-bottom: 120rpx;
  /* 为tabbar留出空间 */
}

/* 现代化头部区域 */
.hero-section {
  padding: 60rpx 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0 0 48rpx 48rpx;
  margin-bottom: 48rpx;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48rpx;
  position: relative;
  z-index: 1;
}

.hero-text {
  flex: 1;
  margin-right: 32rpx;
}

.hero-title {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.hero-subtitle {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
}

.hero-description {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.hero-image {
  flex-shrink: 0;
}

.logo-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.hero-action {
  text-align: center;
  position: relative;
  z-index: 1;
}

.start-btn {
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}

.cta-button:active {
  background: linear-gradient(135deg, #ee5a52, #dc4c64);
  transform: translateY(-2rpx);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.hero-tip {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 16rpx;
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.user-status {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 功能特性区域 */
.features-section {
  padding: 0 32rpx 48rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.section-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.section-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.feature-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

}

.feature-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
  border-color: #e0e7ff;
}


.feature-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.feature-icon {
  font-size: 40rpx;
}

.feature-content {
  text-align: left;
}

.feature-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.feature-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 状态区域 */
.status-section {
  padding: 0 32rpx 48rpx;
}

.status-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

.status-value.success {
  color: #10b981;
}

/* 指南区域 */
.guide-section {
  padding: 0 32rpx 48rpx;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.guide-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.guide-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  font-weight: 700;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.guide-text {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.guide-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
}

.recent-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.recent-section .section-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}

.more-link {
  font-size: 28rpx;
  color: #6366f1;
  font-weight: 500;
}

.recent-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

}

.guide-step:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.recent-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  flex: 1;
  margin-right: 24rpx;
}

.task-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.task-time {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
}

.task-status-wrapper {
  flex-shrink: 0;
}

/* 使用指南区域 */
.guide-section {
  padding: 0 32rpx 48rpx;
}

.guide-section .section-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.guide-steps {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  position: relative;
  margin-bottom: 48rpx;

}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-indicator {
  flex-shrink: 0;
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
}

.step-content {
  flex: 1;
  padding-top: 12rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.step-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.step-connector {
  position: absolute;
  left: 40rpx;
  top: 80rpx;
  width: 4rpx;
  height: 48rpx;
  background: linear-gradient(180deg, #e0e7ff, #c7d2fe);
  border-radius: 2rpx;
  z-index: 1;
}

/* 底部空间 */
.bottom-space {
  height: 48rpx;
}
</style>
